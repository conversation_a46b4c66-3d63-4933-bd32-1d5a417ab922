# Extension Filtering Feature

The batch rename utility now supports filtering files by extension, allowing you to focus on specific file types while ignoring others.

## Usage Examples

### Command Line Usage

#### Process only Python files
```bash
python src/main.py -d /path/to/directory --extensions py
```

#### Process Python and Markdown files
```bash
python src/main.py -d /path/to/directory --extensions py md
```

#### Process text files with various extensions
```bash
python src/main.py -d /path/to/directory --extensions txt md rst
```

#### Extensions with dots (automatically cleaned)
```bash
python src/main.py -d /path/to/directory --extensions .py .md .txt
```

### Interactive Mode

When using the `--prompt` flag, you'll be asked about file extensions:

```bash
python src/main.py --prompt
```

The interactive prompt will ask:
```
 --- File Extensions ---
You can filter files by extension (e.g., py, md, txt)
Leave empty to process all files
File extensions (space-separated): py md
```

## Behavior

### When Extensions Are Specified
- Only files with the specified extensions will be processed
- Extensions are case-insensitive (`.PY` matches `py`)
- Leading dots are automatically removed (`.py` becomes `py`)
- Files without extensions or with non-matching extensions are skipped
- Logging shows which files are skipped and why

### When No Extensions Are Specified
- All files are processed (default behavior)
- Maintains full backward compatibility
- No change from previous versions

## Examples

### Example 1: Python Project Cleanup
```bash
# Only rename Python files in a mixed project
python src/main.py -d ./my_project --extensions py
```

### Example 2: Documentation Files
```bash
# Only process documentation files
python src/main.py -d ./docs --extensions md rst txt
```

### Example 3: Web Development
```bash
# Only process web-related files
python src/main.py -d ./website --extensions html css js
```

## Logging Output

When extension filtering is active, you'll see:

```
INFO: Extension filtering enabled: only processing files with extensions: py, md
DEBUG: Skipping image.jpg (extension 'jpg' not in allowed list)
DEBUG: Skipping config.xml (extension 'xml' not in allowed list)
```

## Integration with Other Features

Extension filtering works seamlessly with all other features:
- Subdirectory inclusion (`--include_subdirs`)
- Depth limiting (`--max-depth`)
- Content preview (`--include-content`)
- Size information (`--include-size`)
- Parallel processing (automatically enabled)

## Performance Benefits

When processing directories with many files:
- Faster processing by skipping unwanted file types
- Reduced memory usage
- Cleaner hash files with only relevant entries
- More focused renaming operations
